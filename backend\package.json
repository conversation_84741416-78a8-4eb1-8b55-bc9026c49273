{"name": "backend", "version": "1.0.0", "main": "app.js", "type": "module", "scripts": {"dev": "nodemon", "start": "node", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.18.0", "mongoose": "^8.17.1", "nodemon": "^3.1.10"}}