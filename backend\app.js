import express from "express";
import cors from "cors";
import authRouter from "./routes/auth.route.js";
import connectToDatabase from "./database/mongobd.js";

const app = express();

// middleware
app.use(express.json());
app.use(cors());

const PORT = 5500;

app.get("/", (req, res) => {
  res.send("Hello there!");
});

app.use("/api/v1/auth", authRouter);

const startServer = async () => {
  try {
    await connectToDatabase();
    console.log("Database connected successfully");
  } catch (error) {
    console.log(
      "Database connection failed, running without database:",
      error.message
    );
  }

  app.listen(PORT, () => {
    console.log(`Server is listening at port http://localhost:${PORT}`);
  });
};

startServer();
