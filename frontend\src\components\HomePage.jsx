import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { authAPI } from '../utils/api';
import './HomePage.css';

const HomePage = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const handleLogout = async () => {
    setLoading(true);
    try {
      // Call backend logout endpoint
      await authAPI.logout();
    } catch (error) {
      console.error('Logout error:', error);
      // Continue with logout even if API call fails
    } finally {
      // Clear local auth state
      logout();
      navigate('/login');
      setLoading(false);
    }
  };

  return (
    <div className="home-container">
      <div className="home-header">
        <h1 className="home-title">Welcome to Your Dashboard</h1>
        <button
          onClick={handleLogout}
          disabled={loading}
          className="logout-button"
        >
          {loading ? 'Logging out...' : 'Logout'}
        </button>
      </div>

      <div className="home-content">
        <div className="user-info-card">
          <h2 className="card-title">User Information</h2>
          <div className="user-details">
            <div className="detail-item">
              <span className="detail-label">Name:</span>
              <span className="detail-value">{user?.name}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Email:</span>
              <span className="detail-value">{user?.email}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Member since:</span>
              <span className="detail-value">
                {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}
              </span>
            </div>
          </div>
        </div>

        <div className="dashboard-cards">
          <div className="dashboard-card">
            <h3 className="card-title">Quick Actions</h3>
            <div className="card-content">
              <p>Welcome to your dashboard! This is where you can manage your account and access various features.</p>
            </div>
          </div>

          <div className="dashboard-card">
            <h3 className="card-title">Recent Activity</h3>
            <div className="card-content">
              <p>No recent activity to display.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
